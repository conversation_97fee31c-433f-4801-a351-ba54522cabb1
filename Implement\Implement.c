/************************************************************
 * ��Ȩ���Ͻ���ҵ��;������ѧϰʹ�á� 
 * �ļ���Implement.c
 * ����: ���Ǿ�
 * ƽ̨: ��Ծ����ҹ���߿�����
 * ΢��: YunXiang_TechShare  
 * Q Q: 2228398717
 * ���ںţ����Ǿ�
************************************************************/

/************************* ͷ�ļ� *************************/

#include "Implement.h"

/************************* �궨�� *************************/

/************************ �������� ************************/

uint16_t time_cnt=0;

/************************ �������� ************************/

/************************************************************ 
 * ����:       show_address(u8 mode)
 * ˵��:       ��LCD����ʾ��ַ��Ϣ 
 * ����:       1 ��ʾDHCP��ȡ���ĵ�ַ
  	           ���� ��ʾ��̬��ַ
 * ���:       ��
 * ����ֵ:     ��
 * ����        ���Ǿ�
 * ����:       ��
************************************************************/

void show_address(u8 mode)
{
	if(mode==1)
	{
		printf("MAC    :%02X.%02X.%02X.%02X.%02X.%02X\r\n",lwipdev.mac[0],lwipdev.mac[1],lwipdev.mac[2],lwipdev.mac[3],lwipdev.mac[4],lwipdev.mac[5]);//��ӡMAC��ַ
		printf("DHCP IP:%d.%d.%d.%d\r\n",lwipdev.ip[0],lwipdev.ip[1],lwipdev.ip[2],lwipdev.ip[3]);						//��ӡ��̬IP��ַ
		printf("DHCP GW:%d.%d.%d.%d\r\n",lwipdev.gateway[0],lwipdev.gateway[1],lwipdev.gateway[2],lwipdev.gateway[3]);	//��ӡ���ص�ַ
		printf("DHCP MASK:%d.%d.%d.%d\r\n",lwipdev.netmask[0],lwipdev.netmask[1],lwipdev.netmask[2],lwipdev.netmask[3]);	//��ӡ���������ַ
	}
	else
	{
		printf("MAC      :%02X.%02X.%02X.%02X.%02X.%02X\r\n",lwipdev.mac[0],lwipdev.mac[1],lwipdev.mac[2],lwipdev.mac[3],lwipdev.mac[4],lwipdev.mac[5]);//��ӡMAC��ַ
		printf("Static IP:%d.%d.%d.%d\r\n",lwipdev.ip[0],lwipdev.ip[1],lwipdev.ip[2],lwipdev.ip[3]);						//��ӡ��̬IP��ַ
		printf("Static GW:%d.%d.%d.%d\r\n",lwipdev.gateway[0],lwipdev.gateway[1],lwipdev.gateway[2],lwipdev.gateway[3]);	//��ӡ���ص�ַ
		printf("Static MASK:%d.%d.%d.%d\r\n",lwipdev.netmask[0],lwipdev.netmask[1],lwipdev.netmask[2],lwipdev.netmask[3]);	//��ӡ���������ַ
	}
}

/************************************************************ 
 * ����:       System_Init(void)
 * ˵��:       ϵͳ��ʼ��
 * ����:       ��
 * ���:       ��
 * ����ֵ:     ��
 * ����        ���Ǿ�
 * ����:       ��
************************************************************/

void System_Init(void)
{
    systick_config();     // ʱ������
	
	  nvic_priority_group_set(NVIC_PRIGROUP_PRE2_SUB2);//����NVIC�жϷ���2:2λ��ռ���ȼ���2λ��Ӧ���ȼ�
	
	  USART0_Config();  // ���ڳ�ʼ��
	
	  LED_Init();  			//LED��ʼ��
	
//	  KEY_Init();  			//������ʼ��
//	

	  my_mem_init(SRAMIN);		//��ʼ���ڲ��ڴ��
	  my_mem_init(SRAMCCM);	//��ʼ��CCM�ڴ��

		printf("LYIT GD32F4\r\n");
		printf("Ethernet lwIP Test\r\n");
		printf("LYIT@GD32F470\r\n");
		printf("2024/03/11\r\n");
		Timer3_Init(9999,239); //100hz��Ƶ��

		while(lwip_comm_init()) //lwip��ʼ��
		{
			printf("LWIP Init Failed!\r\n");
			delay_1ms(1200);
			printf("Retrying...\r\n");
		}
    
		#if LWIP_DHCP   //ʹ��DHCP
			while((lwipdev.dhcpstatus!=2)&&(lwipdev.dhcpstatus!=0XFF))//�ȴ�DHCP��ȡ�ɹ�/��ʱ���
			{
				lwip_periodic_handle();	//LWIP�ں���Ҫ��ʱ�����ĺ���
			}
		#endif
		  show_address(lwipdev.dhcpstatus);	//��ʾ��ַ��Ϣ

}

/************************************************************ 
 * ����:       Implement(void)
 * ˵��:       ִ�к���
 * ����:       ��
 * ���:       ��
 * ����ֵ:     ��
 * ����        ���Ǿ�
 * ����:       ��
************************************************************/

void Implement(void)
{
	 while(1)
	 {
        lwip_periodic_handle();	//LWIP�ں���Ҫ��ʱ�����ĺ���
	 }
}


/****************************End*****************************/

